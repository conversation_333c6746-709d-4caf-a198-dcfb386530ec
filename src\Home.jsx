import React, { useEffect, useState } from 'react';

const Home = ({ onTryNow }) => {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <div style={styles.body}>
      <div style={styles.particles}>
        {Array.from({ length: 20 }, (_, i) => (
          <div
            key={i}
            style={{
              ...styles.particle,
              left: Math.random() * 100 + 'vw',
              top: Math.random() * 100 + 'vh',
              animationDelay: Math.random() * 5 + 's',
              width: i % 2 === 0 ? '5px' : '3px',
              height: i % 2 === 0 ? '5px' : '3px',
              animationDuration: i % 2 === 0 ? '15s' : '12s'
            }}
          />
        ))}
      </div>

      <div style={styles.container}>
        <div style={{
          ...styles.logoContainer,
          ...(isLoaded ? styles.logoContainerLoaded : {})
        }}>
          <div style={styles.logoGlow}></div>
          <img
            style={styles.logo}
            src="/logo-only.png"
            alt="ViaTryon Logo"
          />
        </div>

        <div style={styles.text}>
          <h1 style={{
            ...styles.title,
            ...(isLoaded ? styles.titleLoaded : {})
          }}>
            ViaTryon
          </h1>
          <p style={{
            ...styles.subtitle,
            ...(isLoaded ? styles.subtitleLoaded : {})
          }}>
            Try Before You Buy - Virtually
          </p>
        </div>

        <button
          style={{
            ...styles.tryNowBtn,
            ...(isLoaded ? styles.tryNowBtnLoaded : {})
          }}
          onClick={onTryNow}
          onMouseEnter={(e) => {
            e.target.style.transform = 'scale(1.08) translateY(-5px)';
            e.target.style.boxShadow = '0 25px 50px rgba(45, 140, 136, 0.6), 0 10px 25px rgba(0, 0, 0, 0.2)';
            e.target.style.background = 'linear-gradient(135deg, #1f6b68, #2D8C88)';
          }}
          onMouseLeave={(e) => {
            e.target.style.transform = 'scale(1) translateY(0)';
            e.target.style.boxShadow = '0 15px 35px rgba(45, 140, 136, 0.4), 0 5px 15px rgba(0, 0, 0, 0.1)';
            e.target.style.background = 'linear-gradient(135deg, #2D8C88, #1f6b68)';
          }}
        >
          Try Now
        </button>
      </div>
    </div>
  );
};

const styles = {
  body: {
    margin: 0,
    height: '100vh',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    background: 'radial-gradient(circle at center, #1e3a3a, #0a1a1a)',
    overflow: 'hidden',
    fontFamily: "'Inter', 'Segoe UI', 'Roboto', sans-serif",
    position: 'relative'
  },

  container: {
    textAlign: 'center',
    position: 'relative',
    zIndex: 1
  },

  particles: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    top: 0,
    left: 0,
    overflow: 'hidden',
    zIndex: 0
  },

  particle: {
    position: 'absolute',
    background: 'rgba(45, 140, 136, 0.6)',
    borderRadius: '50%',
    animation: 'float 10s infinite ease-in-out'
  },

  logoContainer: {
    opacity: 0,
    transform: 'scale(0.1) rotate(-180deg)',
    transition: 'all 2s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
    position: 'relative',
    marginBottom: '40px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    perspective: '1000px'
  },

  logoContainerLoaded: {
    opacity: 1,
    transform: 'scale(1) rotate(0deg)'
  },

  logoGlow: {
    position: 'absolute',
    width: '300px',
    height: '300px',
    background: 'radial-gradient(circle, rgba(45, 140, 136, 0.6), rgba(45, 140, 136, 0.2), transparent)',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    borderRadius: '50%',
    animation: 'advancedGlow 3s infinite ease-in-out',
    filter: 'blur(20px)'
  },

  logo: {
    width: '220px',
    height: 'auto',
    position: 'relative',
    zIndex: 2,
    display: 'block',
    margin: '0 auto',
    filter: 'drop-shadow(0 10px 30px rgba(45, 140, 136, 0.3))',
    animation: 'logoFloat 4s infinite ease-in-out'
  },

  text: {
    position: 'relative',
    overflow: 'hidden',
    marginBottom: '30px'
  },

  title: {
    color: '#2D8C88',
    fontSize: '56px',
    margin: '20px 0',
    opacity: 0,
    transform: 'translateY(50px) scale(0.8)',
    transition: 'all 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) 1.5s',
    fontWeight: '800',
    letterSpacing: '3px',
    textShadow: '0 5px 15px rgba(45, 140, 136, 0.3)',
    background: 'linear-gradient(135deg, #2D8C88, #1f6b68, #2D8C88)',
    backgroundClip: 'text',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    animation: 'textShimmer 3s infinite ease-in-out'
  },

  titleLoaded: {
    opacity: 1,
    transform: 'translateY(0) scale(1)'
  },

  subtitle: {
    color: '#2D8C88',
    fontSize: '28px',
    margin: 0,
    opacity: 0,
    transform: 'translateY(40px) rotateX(90deg)',
    transition: 'all 1.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) 2s',
    fontWeight: '500',
    letterSpacing: '2px',
    textShadow: '0 3px 10px rgba(45, 140, 136, 0.2)',
    perspective: '1000px'
  },

  subtitleLoaded: {
    opacity: 1,
    transform: 'translateY(0) rotateX(0deg)'
  },

  tryNowBtn: {
    marginTop: '40px',
    padding: '18px 50px',
    fontSize: '22px',
    color: 'white',
    background: 'linear-gradient(135deg, #2D8C88, #1f6b68)',
    border: 'none',
    borderRadius: '50px',
    cursor: 'pointer',
    opacity: 0,
    transform: 'scale(0.3) translateY(100px)',
    transition: 'all 1.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) 2.5s, transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275), box-shadow 0.4s',
    fontWeight: '700',
    letterSpacing: '2px',
    boxShadow: '0 15px 35px rgba(45, 140, 136, 0.4), 0 5px 15px rgba(0, 0, 0, 0.1)',
    position: 'relative',
    overflow: 'hidden'
  },

  tryNowBtnLoaded: {
    opacity: 1,
    transform: 'scale(1) translateY(0)'
  }
};

// Add Apple-style CSS animations
const styleSheet = document.createElement('style');
styleSheet.type = 'text/css';
styleSheet.innerText = `
  @keyframes advancedGlow {
    0% {
      transform: translate(-50%, -50%) scale(0.8) rotate(0deg);
      opacity: 0.3;
      filter: blur(20px) hue-rotate(0deg);
    }
    33% {
      transform: translate(-50%, -50%) scale(1.2) rotate(120deg);
      opacity: 0.7;
      filter: blur(15px) hue-rotate(60deg);
    }
    66% {
      transform: translate(-50%, -50%) scale(1.1) rotate(240deg);
      opacity: 0.5;
      filter: blur(25px) hue-rotate(120deg);
    }
    100% {
      transform: translate(-50%, -50%) scale(0.8) rotate(360deg);
      opacity: 0.3;
      filter: blur(20px) hue-rotate(180deg);
    }
  }

  @keyframes logoFloat {
    0% { transform: translateY(0px) rotateY(0deg); }
    25% { transform: translateY(-10px) rotateY(5deg); }
    50% { transform: translateY(-5px) rotateY(0deg); }
    75% { transform: translateY(-15px) rotateY(-5deg); }
    100% { transform: translateY(0px) rotateY(0deg); }
  }

  @keyframes textShimmer {
    0% { background-position: -200% center; }
    100% { background-position: 200% center; }
  }

  @keyframes float {
    0% {
      transform: translate(0, 100vh) scale(0);
      opacity: 0;
      filter: blur(5px);
    }
    10% {
      opacity: 1;
      filter: blur(0px);
    }
    90% {
      opacity: 0.8;
      filter: blur(2px);
    }
    100% {
      transform: translate(200px, -100px) scale(1.5);
      opacity: 0;
      filter: blur(10px);
    }
  }
`;
document.head.appendChild(styleSheet);

export default Home;

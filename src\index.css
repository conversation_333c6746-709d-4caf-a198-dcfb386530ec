@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: #1F2937;
  background-color: #F9FAFB;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Mobile-First Responsive Utilities */
@layer utilities {
  /* Touch-friendly targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Safe area for mobile devices */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Smooth scrolling */
  .smooth-scroll {
    scroll-behavior: smooth;
  }

  /* Focus styles for accessibility */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-green focus:ring-offset-2;
  }

  /* Button variants */
  .btn-primary {
    @apply bg-primary-green text-white px-6 py-3 rounded-full font-medium transition-all duration-200 hover:bg-primary-orange focus-ring min-h-[48px] flex items-center justify-center;
  }

  .btn-secondary {
    @apply bg-white border-2 border-primary-green text-primary-green px-6 py-3 rounded-full font-medium transition-all duration-200 hover:bg-primary-green hover:text-white focus-ring min-h-[48px] flex items-center justify-center;
  }

  .btn-outline {
    @apply bg-transparent border-2 border-primary-orange text-primary-orange px-6 py-3 rounded-full font-medium transition-all duration-200 hover:bg-primary-orange hover:text-white focus-ring min-h-[48px] flex items-center justify-center;
  }

  /* Card styles */
  .card {
    @apply bg-white rounded-xl shadow-md border border-gray-200 overflow-hidden;
  }

  .card-hover {
    @apply transition-all duration-200 hover:shadow-lg hover:scale-105;
  }

  /* Typography utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Loading states */
  .loading-spinner {
    @apply animate-spin rounded-full border-t-2 border-b-2 border-primary-green;
  }

  /* Mobile-specific utilities */
  @media (max-width: 768px) {
    .mobile-padding {
      @apply px-4;
    }

    .mobile-text-sm {
      @apply text-sm;
    }

    .mobile-text-base {
      @apply text-base;
    }

    .mobile-grid-1 {
      @apply grid-cols-1;
    }

    .mobile-space-y-4 {
      @apply space-y-4;
    }
  }

  /* Tablet-specific utilities */
  @media (min-width: 768px) and (max-width: 1024px) {
    .tablet-grid-2 {
      @apply grid-cols-2;
    }

    .tablet-padding {
      @apply px-6;
    }
  }

  /* Desktop-specific utilities */
  @media (min-width: 1024px) {
    .desktop-grid-3 {
      @apply grid-cols-3;
    }

    .desktop-padding {
      @apply px-8;
    }
  }
}

/* Component Styles */
@layer components {
  /* Navigation */
  .nav-link {
    @apply text-gray-700 hover:text-primary-orange transition-colors duration-200 font-medium;
  }

  .nav-link-active {
    @apply text-primary-green;
  }

  /* Hero sections */
  .hero-title {
    @apply text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-serif text-gray-800 leading-tight;
  }

  .hero-subtitle {
    @apply text-base md:text-lg lg:text-xl text-gray-700 font-light leading-relaxed;
  }

  /* Section headers */
  .section-title {
    @apply text-2xl md:text-3xl lg:text-4xl font-serif text-gray-800 mb-4;
  }

  .section-subtitle {
    @apply text-gray-700 max-w-2xl mx-auto font-sans text-sm md:text-base;
  }

  /* Product grids */
  .product-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8;
  }

  .product-card {
    @apply card card-hover h-full flex flex-col;
  }

  .product-image-container {
    @apply relative h-56 md:h-72 bg-gray-50 flex items-center justify-center p-4 md:p-8;
  }

  .product-image {
    @apply h-40 md:h-56 object-contain transition-transform duration-200 group-hover:scale-105;
  }

  .product-content {
    @apply p-4 md:p-6 flex-grow;
  }

  /* Form elements */
  .form-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-transparent transition-colors duration-200;
  }

  .form-textarea {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-transparent transition-colors duration-200 resize-vertical;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }

  /* Animations */
  .fade-in {
    @apply animate-fade-in;
  }

  .slide-up {
    @apply animate-slide-up;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn-primary {
    @apply border-2 border-black;
  }

  .btn-secondary {
    @apply border-2 border-black;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Switch Component Styles */
.switch-container {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch-container input {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  transition: .4s;
  border-radius: 34px;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.switch-slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

input:checked + .switch-slider {
  background-color: #2D8C88;
}

input:checked + .switch-slider:before {
  transform: translateX(26px);
}

.switch-label {
  color: #2D8C88;
  font-weight: 700;
  font-size: 14px;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  margin-bottom: 8px;
}